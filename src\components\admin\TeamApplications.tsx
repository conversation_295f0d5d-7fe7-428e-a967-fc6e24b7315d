'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { db, TeamApplication } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';

interface TeamApplicationsProps {
  applications: TeamApplication[];
  onUpdate: () => void;
  onRefresh: () => void;
}

export default function TeamApplications({ applications, onUpdate, onRefresh }: TeamApplicationsProps) {
  const { user } = useAuth();
  const [selectedApplication, setSelectedApplication] = useState<TeamApplication | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState<string>('all');

  const handleStatusUpdate = async (application: TeamApplication, newStatus: string, adminNotes?: string) => {
    try {
      setLoading(true);
      const updated = await db.updateTeamApplication(application.id, {
        status: newStatus as any,
        admin_notes: adminNotes || application.admin_notes,
        reviewed_by: user?.id
      });

      if (!updated) {
        throw new Error('Failed to update application');
      }

      onUpdate();
      setSelectedApplication(null);
    } catch (error: any) {
      console.error('Error updating application:', error);
      setError(error.message || 'Failed to update application');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (application: TeamApplication) => {
    if (!window.confirm(`Are you sure you want to delete ${application.name}'s application?`)) {
      return;
    }

    try {
      setLoading(true);
      const success = await db.deleteTeamApplication(application.id);
      
      if (!success) {
        throw new Error('Failed to delete application');
      }

      onUpdate();
    } catch (error: any) {
      console.error('Error deleting application:', error);
      setError(error.message || 'Failed to delete application');
    } finally {
      setLoading(false);
    }
  };

  const filteredApplications = applications.filter(app => {
    if (filter === 'all') return true;
    return app.status === filter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'reviewing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header & Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Team Applications ({applications.length})
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Review and manage team member applications
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <label htmlFor="filter" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Filter:
          </label>
          <select
            id="filter"
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All Applications</option>
            <option value="pending">Pending</option>
            <option value="reviewing">Reviewing</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="withdrawn">Withdrawn</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
        </div>
      )}

      {/* Applications List */}
      {filteredApplications.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📝</div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No Applications Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {filter === 'all' 
              ? 'No team applications have been submitted yet.'
              : `No applications with status "${filter}" found.`
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
          {filteredApplications.map((application) => (
            <motion.div
              key={application.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -2 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden"
            >
              {/* Header */}
              <div className="p-3 sm:p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-start justify-between gap-3">
                  <div className="min-w-0 flex-1">
                    <h4 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white truncate">
                      {application.name}
                    </h4>
                    <p className="text-blue-600 dark:text-blue-400 font-medium text-sm truncate">
                      {application.desired_role}
                    </p>
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 truncate">
                      {application.email}
                    </p>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                      {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(application.priority)}`}>
                      {application.priority.charAt(0).toUpperCase() + application.priority.slice(1)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Experience:</span>
                    <span className="ml-2 text-sm text-gray-600 dark:text-gray-400 capitalize">
                      {application.experience_level}
                    </span>
                  </div>
                  
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Availability:</span>
                    <span className="ml-2 text-sm text-gray-600 dark:text-gray-400 capitalize">
                      {application.availability}
                    </span>
                  </div>

                  {application.location && (
                    <div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Location:</span>
                      <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                        {application.location}
                      </span>
                    </div>
                  )}

                  {application.skills && application.skills.length > 0 && (
                    <div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1">Skills:</span>
                      <div className="flex flex-wrap gap-1">
                        {application.skills.slice(0, 3).map((skill, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
                          >
                            {skill}
                          </span>
                        ))}
                        {application.skills.length > 3 && (
                          <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                            +{application.skills.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1">Motivation:</span>
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                      {application.motivation}
                    </p>
                  </div>

                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Applied: {new Date(application.created_at).toLocaleDateString()}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setSelectedApplication(application)}
                    className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded text-sm hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  >
                    View Details
                  </button>
                  
                  {application.status === 'pending' && (
                    <>
                      <button
                        onClick={() => handleStatusUpdate(application, 'reviewing')}
                        disabled={loading}
                        className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300 rounded text-sm hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors"
                      >
                        Start Review
                      </button>
                      <button
                        onClick={() => handleStatusUpdate(application, 'approved')}
                        disabled={loading}
                        className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded text-sm hover:bg-green-200 dark:hover:bg-green-800 transition-colors"
                      >
                        Approve
                      </button>
                      <button
                        onClick={() => handleStatusUpdate(application, 'rejected')}
                        disabled={loading}
                        className="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded text-sm hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
                      >
                        Reject
                      </button>
                    </>
                  )}
                  
                  <button
                    onClick={() => handleDelete(application)}
                    disabled={loading}
                    className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Loading Overlay */}
      {loading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-gray-900 dark:text-white">Processing...</span>
          </div>
        </div>
      )}
    </div>
  );
}
