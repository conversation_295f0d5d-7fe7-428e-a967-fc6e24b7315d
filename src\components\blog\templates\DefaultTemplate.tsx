import Link from 'next/link';
import { BlogPost } from '@/lib/supabase';

interface DefaultTemplateProps {
  post: BlogPost;
}

export default function DefaultTemplate({ post }: DefaultTemplateProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <article className="max-w-4xl mx-auto">
      {/* Header */}
      <header className="mb-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <ol className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <li>
              <Link href="/" className="hover:text-blue-600 dark:hover:text-blue-400">
                Home
              </Link>
            </li>
            <li>/</li>
            <li>
              <Link href="/blog" className="hover:text-blue-600 dark:hover:text-blue-400">
                Blog
              </Link>
            </li>
            <li>/</li>
            <li className="text-gray-900 dark:text-white">{post.title}</li>
          </ol>
        </nav>

        {/* Category and Meta */}
        <div className="flex items-center mb-4">
          {post.category && (
            <Link
              href={`/blog?category=${post.category.slug}`}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
              style={{ 
                backgroundColor: post.category.color + '20',
                color: post.category.color
              }}
            >
              {post.category.icon && <span className="mr-1">{post.category.icon}</span>}
              {post.category.name}
            </Link>
          )}
          <span className="mx-3 text-gray-400">•</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {post.reading_time} min read
          </span>
          <span className="mx-3 text-gray-400">•</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {post.view_count} views
          </span>
        </div>

        {/* Title */}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
          {post.title}
        </h1>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
            {post.excerpt}
          </p>
        )}

        {/* Author and Date */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 mr-4">
              {post.author?.display_name 
                ? post.author.display_name.split(' ').map(n => n[0]).join('')
                : 'A'
              }
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">
                {post.author?.display_name || 'Admin'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Published on {formatDate(post.published_at || post.created_at)}
                {post.updated_at !== post.created_at && (
                  <span> • Updated {formatDate(post.updated_at)}</span>
                )}
              </p>
            </div>
          </div>

          {/* Share Buttons */}
          <div className="flex items-center space-x-2">
            <button 
              onClick={() => {
                const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(post.title)}&url=${encodeURIComponent(window.location.href)}`;
                window.open(url, '_blank');
              }}
              className="p-2 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
            >
              <span className="sr-only">Share on Twitter</span>
              🐦
            </button>
            <button 
              onClick={() => {
                const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`;
                window.open(url, '_blank');
              }}
              className="p-2 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
            >
              <span className="sr-only">Share on LinkedIn</span>
              💼
            </button>
            <button 
              onClick={() => {
                navigator.clipboard.writeText(window.location.href);
                alert('Link copied to clipboard!');
              }}
              className="p-2 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
            >
              <span className="sr-only">Copy link</span>
              🔗
            </button>
          </div>
        </div>
      </header>

      {/* Featured Image */}
      {post.featured_image && (
        <div className="mb-12">
          <img
            src={post.featured_image.file_path}
            alt={post.featured_image.alt_text || post.title}
            className="w-full h-64 md:h-96 object-cover rounded-xl shadow-lg"
          />
          {post.featured_image.caption && (
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-2">
              {post.featured_image.caption}
            </p>
          )}
        </div>
      )}

      {/* Content */}
      <div className="prose prose-lg max-w-none dark:prose-invert prose-blue mb-12">
        <div dangerouslySetInnerHTML={{ __html: post.content }} />
      </div>

      {/* Tags */}
      {post.tags && post.tags.length > 0 && (
        <div className="mb-12 pt-8 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Tags
          </h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Link
                key={tag.id}
                href={`/blog?tag=${tag.slug}`}
                className="px-3 py-1 rounded-full text-sm font-medium hover:opacity-80 transition-opacity"
                style={{ 
                  backgroundColor: tag.color + '20',
                  color: tag.color
                }}
              >
                #{tag.name}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* External Links */}
      {post.external_links && Object.keys(post.external_links).length > 0 && (
        <div className="mb-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-xl">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Related Links
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {post.external_links.github && (
              <a
                href={post.external_links.github}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow"
              >
                <span className="mr-3">🔗</span>
                <span className="font-medium">GitHub Repository</span>
              </a>
            )}
            {post.external_links.demo && (
              <a
                href={post.external_links.demo}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow"
              >
                <span className="mr-3">🌐</span>
                <span className="font-medium">Live Demo</span>
              </a>
            )}
            {post.external_links.playstore && (
              <a
                href={post.external_links.playstore}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow"
              >
                <span className="mr-3">📱</span>
                <span className="font-medium">Play Store</span>
              </a>
            )}
            {post.external_links.youtube && (
              <a
                href={post.external_links.youtube}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center p-3 bg-white dark:bg-gray-700 rounded-lg hover:shadow-md transition-shadow"
              >
                <span className="mr-3">📺</span>
                <span className="font-medium">YouTube Video</span>
              </a>
            )}
          </div>
        </div>
      )}

      {/* Author Bio */}
      <div className="mb-12 p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          About the Author
        </h3>
        <div className="flex items-center mb-3">
          <div className="w-16 h-16 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 mr-4">
            {post.author?.display_name 
              ? post.author.display_name.split(' ').map(n => n[0]).join('')
              : 'A'
            }
          </div>
          <div>
            <p className="font-medium text-gray-900 dark:text-white text-lg">
              {post.author?.display_name || 'Admin'}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {post.author?.position || 'Content Creator'}
            </p>
          </div>
        </div>
        {post.author?.bio && (
          <p className="text-gray-600 dark:text-gray-400">
            {post.author.bio}
          </p>
        )}
      </div>

      {/* Back to Blog */}
      <div className="text-center">
        <Link
          href="/blog"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          ← Back to Blog
        </Link>
      </div>
    </article>
  );
}
